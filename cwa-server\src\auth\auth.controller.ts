import { Body, Controller, Post, UseGuards, Req } from "@nestjs/common";
import { AuthService } from "./auth.service";
import { IAuth } from "./interfaces/auth.interfaces";
import { CreateUserDto } from "src/users/dto/createUser.dto";
import { signInDto } from "./dto/signIn.dto";

import { ForgotPasswordDto } from "./dto/forget-password.dto";
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from "./dto/change-password.dto";
import { VerifyOtpDTO } from "./dto/verify-otp.dto";
import { AuthGuard } from "./auth.guard";

@Controller('auth')
export class AuthController{
    constructor(private readonly authService:AuthService){}

    @Post("register")
    
    async signUp(@Body() signUpDto:CreateUserDto):Promise<IAuth>{
        return this.authService.signUp(signUpDto);
    }

    @Post("login")
    
    async signIn(@Body() signInDto:signInDto):Promise<IAuth>{
        return this.authService.signIn(signInDto);
    }

    @Post('forgot-password')
   
    async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
        return this.authService.forgotPassword(forgotPasswordDto);
    }

    @Post('verify-otp')
    
@Post('reset-password')
   
    async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
        return this.authService.resetPassword(resetPasswordDto);
    }

    @UseGuards(AuthGuard)
    @Post('change-password')

    async changePassword(
        @Req() req,
        @Body() changePasswordDto: ChangePasswordDto,
        
    ) {
        const userId = req.user._id;
        return this.authService.changePassword(
            userId,
            changePasswordDto,
        );
    }
}